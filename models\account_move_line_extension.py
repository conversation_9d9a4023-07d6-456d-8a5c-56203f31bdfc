# -*- coding: utf-8 -*-
"""
Account Move Line Extension for Tobacco Sales Reporting

This module extends account.move.line to add a manual stick count field
for cigar products that carries over from sale order lines, ensuring
consistent tracking from sales to invoicing.

IMPORTANT: NO FIELD DEFINITIONS IN PYTHON CODE!
The x_cigar_stick_count field must be created via Odoo Studio only.
This prevents interference with accounting calculations.
"""

from odoo import models, fields, api


class AccountMoveLine(models.Model):
    """
    Extends account.move.line to provide stick count functionality for cigars.

    This extension provides methods to work with the x_cigar_stick_count field
    that should be created via Odoo Studio or XML data files, not Python field definitions.
    """
    _inherit = 'account.move.line'

    def get_cigar_stick_count(self):
        """
        Get the cigar stick count for this line.

        This method safely retrieves the stick count value, handling cases
        where the field might not exist or be accessible.

        Returns:
            int: The stick count value, or 0 if not available
        """
        return getattr(self, 'x_cigar_stick_count', 0)

    def set_cigar_stick_count(self, count):
        """
        Set the cigar stick count for this line.

        This method safely sets the stick count value, handling cases
        where the field might not exist.

        Args:
            count (int): The stick count to set
        """
        if hasattr(self, 'x_cigar_stick_count'):
            self.x_cigar_stick_count = count

    def transfer_stick_count_from_sale_line(self, sale_line):
        """
        Transfer stick count from a sale order line to this invoice line.

        This method should be called when creating invoice lines from sale orders
        to preserve the manually entered stick count.

        Args:
            sale_line: sale.order.line record
        """
        if hasattr(sale_line, 'x_cigar_stick_count') and hasattr(self, 'x_cigar_stick_count'):
            self.x_cigar_stick_count = sale_line.x_cigar_stick_count

    @api.model_create_multi
    def create(self, vals_list):
        """
        Override create to automatically transfer stick count from sale order lines.
        """
        lines = super().create(vals_list)

        # Transfer stick count from sale order lines if available
        for line in lines:
            if (line.sale_line_ids and
                hasattr(line, 'x_cigar_stick_count') and
                line.product_id and
                hasattr(line.product_id, 'is_cigar_category') and
                line.product_id.is_cigar_category()):

                # Get the first sale line (usually there's only one)
                sale_line = line.sale_line_ids[0]
                if hasattr(sale_line, 'x_cigar_stick_count'):
                    stick_count = getattr(sale_line, 'x_cigar_stick_count', 0)
                    if stick_count > 0:
                        line.x_cigar_stick_count = stick_count

        return lines

    def write(self, vals):
        """
        Override write to handle stick count transfer when sale_line_ids is updated.
        """
        result = super().write(vals)

        # If sale_line_ids was updated, transfer stick count
        if 'sale_line_ids' in vals:
            for line in self:
                if (line.sale_line_ids and
                    hasattr(line, 'x_cigar_stick_count') and
                    line.product_id and
                    hasattr(line.product_id, 'is_cigar_category') and
                    line.product_id.is_cigar_category()):

                    sale_line = line.sale_line_ids[0]
                    if hasattr(sale_line, 'x_cigar_stick_count'):
                        stick_count = getattr(sale_line, 'x_cigar_stick_count', 0)
                        if stick_count > 0:
                            line.x_cigar_stick_count = stick_count

        return result

    def update_stick_count_from_sale_lines(self):
        """
        Update stick count for existing invoice lines from their linked sale order lines.

        This method can be used to fix existing invoices that don't have stick count
        populated but have linked sale order lines with stick count data.

        Returns:
            dict: Summary of updates made
        """
        updated_count = 0
        skipped_count = 0

        for line in self:
            # Only process cigar products with linked sale lines
            if (line.sale_line_ids and
                hasattr(line, 'x_cigar_stick_count') and
                line.product_id and
                hasattr(line.product_id, 'is_cigar_category') and
                line.product_id.is_cigar_category()):

                # Get stick count from sale line
                sale_line = line.sale_line_ids[0]
                if hasattr(sale_line, 'x_cigar_stick_count'):
                    sale_stick_count = getattr(sale_line, 'x_cigar_stick_count', 0)
                    current_stick_count = getattr(line, 'x_cigar_stick_count', 0)

                    # Update if sale line has stick count and invoice line doesn't
                    if sale_stick_count > 0 and current_stick_count == 0:
                        line.x_cigar_stick_count = sale_stick_count
                        updated_count += 1
                    else:
                        skipped_count += 1
                else:
                    skipped_count += 1
            else:
                skipped_count += 1

        return {
            'updated': updated_count,
            'skipped': skipped_count,
            'total_processed': len(self)
        }

    @api.model
    def fix_missing_stick_counts_bulk(self, date_from=None, date_to=None, partner_ids=None, only_posted=True):
        """
        Bulk fix missing stick counts for invoice lines in a date range.

        This method finds all invoice lines for cigar products that have linked sale order lines
        with stick count data but are missing stick count on the invoice line, and updates them.

        Args:
            date_from (date): Start date for invoice search
            date_to (date): End date for invoice search
            partner_ids (list): List of partner IDs to filter by
            only_posted (bool): Only process posted invoices

        Returns:
            dict: Summary of updates made
        """
        # Build domain for invoices
        domain = [('type', '=', 'out_invoice')]

        if date_from:
            domain.append(('invoice_date', '>=', date_from))
        if date_to:
            domain.append(('invoice_date', '<=', date_to))
        if only_posted:
            domain.append(('state', '=', 'posted'))
        if partner_ids:
            domain.append(('partner_id', 'in', partner_ids))

        invoices = self.env['account.move'].search(domain)

        # Find lines that need fixing
        lines_to_fix = self.env['account.move.line']

        for invoice in invoices:
            for line in invoice.invoice_line_ids:
                # Check if this line needs fixing
                if (line.product_id and
                    hasattr(line.product_id, 'is_cigar_category') and
                    line.product_id.is_cigar_category() and
                    hasattr(line, 'x_cigar_stick_count') and
                    line.sale_line_ids):

                    # Current stick count must be 0 or empty
                    current_stick_count = getattr(line, 'x_cigar_stick_count', 0)
                    if current_stick_count == 0:
                        # Sale line must have stick count data
                        sale_line = line.sale_line_ids[0]
                        if hasattr(sale_line, 'x_cigar_stick_count'):
                            sale_stick_count = getattr(sale_line, 'x_cigar_stick_count', 0)
                            if sale_stick_count > 0:
                                lines_to_fix |= line

        # Update the lines
        if lines_to_fix:
            result = lines_to_fix.update_stick_count_from_sale_lines()
            result['invoices_processed'] = len(lines_to_fix.mapped('move_id'))
            result['customers_affected'] = len(lines_to_fix.mapped('partner_id'))
        else:
            result = {
                'updated': 0,
                'skipped': 0,
                'total_processed': 0,
                'invoices_processed': 0,
                'customers_affected': 0
            }

        return result




