<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Add Cigar Stick Count field to Invoice Form View -->
        <record id="view_move_form_cigar_stick_count" model="ir.ui.view">
            <field name="name">account.move.form.cigar.stick.count</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_move_form"/>
            <field name="arch" type="xml">
                <field name="quantity" position="after">
                    <field name="is_cigar_product" invisible="1"/>
                    <field name="x_cigar_stick_count"
                           string="Cigar Sticks"
                           optional="show"
                           attrs="{'invisible': [('is_cigar_product', '=', False)], 'readonly': [('is_cigar_product', '=', False)]}"
                           help="Manual entry for actual number of cigar sticks being invoiced (only for cigar products)"/>
                </field>
            </field>
        </record>

    </data>
</odoo>
