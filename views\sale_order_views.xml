<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Add Cigar Stick Count field to Sale Order Form View -->
        <record id="view_order_form_cigar_stick_count" model="ir.ui.view">
            <field name="name">sale.order.form.cigar.stick.count</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_order_form"/>
            <field name="arch" type="xml">
                <field name="product_uom_qty" position="after">
                    <field name="is_cigar_product" invisible="1"/>
                    <field name="x_cigar_stick_count"
                           string="Cigar Sticks"
                           optional="show"
                           attrs="{'invisible': [('is_cigar_product', '=', False)], 'readonly': [('is_cigar_product', '=', False)]}"
                           help="Manual entry for actual number of cigar sticks being sold (only for cigar products)"/>
                </field>
            </field>
        </record>

    </data>
</odoo>
